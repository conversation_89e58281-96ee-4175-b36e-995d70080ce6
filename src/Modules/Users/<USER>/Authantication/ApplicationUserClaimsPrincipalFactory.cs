using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Users.Application.Abstractions;
using Users.Domain.Account;
using Users.Infrastructure.Data;

namespace Users.Infrastructure.Authantication;

public class ApplicationUserClaimsPrincipalFactory(
    UserManager<User> userManager,
    RoleManager<Role> roleManager,
    IOptions<IdentityOptions> options,
    IUserDbContext dbContext
) : UserClaimsPrincipalFactory<User, Role>(userManager, roleManager, options)
{
    protected override async Task<ClaimsIdentity> GenerateClaimsAsync(User user)
    {
        var identity = await base.GenerateClaimsAsync(user);
        var userProfile = await dbContext
            .Users
            .Include(x => x.UserRole)
            .ThenInclude(x => x.Role)
            .FirstOrDefaultAsync(p => p.Id == user.Id);
        identity.AddClaim(new Claim("FullName", $"{user.Name} {user.Surname}"));
        identity.AddClaim(new Claim(ClaimTypes.Surname, $"{user.Surname}"));
        identity.AddClaim(new Claim("InsertDate", user.InsertDate.ToString("yyyy-MM-dd")));
        identity.AddClaim(new Claim(ClaimTypes.MobilePhone, user.PhoneNumber ?? ""));
        if (userProfile != null)
        {
            userProfile.UserRole ??= [];
            foreach (var userrole in userProfile.UserRole.Where(x => !string.IsNullOrWhiteSpace(x.Role?.Name)))
            {
                var roleName = userrole?.Role?.Name ?? "";
                if (!identity.Claims.Any(x => x.Type == ClaimTypes.Role && x.Value == roleName))
                {
                    identity.AddClaim(new Claim(ClaimTypes.Role, roleName));
                }
                var roleNormalizedName = userrole?.Role?.NormalizedName ?? "";
                if (!identity.Claims.Any(x => x.Type == "RoleNormalizedName" && x.Value == roleNormalizedName))
                {
                    identity.AddClaim(new Claim("RoleNormalizedName", roleNormalizedName));
                }
                var roleId = userrole?.RoleId.ToString() ?? "";
                if (!identity.Claims.Any(x => x.Type == "RoleId" && x.Value == roleId))
                {
                    identity.AddClaim(new Claim("RoleId", roleId));
                }
            }
        }
        return identity;
    }
}
