using MediatR;
using Microsoft.AspNetCore.Http;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.CreatePublicTicket;

public record CreatePublicTicketCommand : IRequest<Result<Guid>>
{
    public required Guid CustomerId { get; init; }
    public required string Title { get; init; }
    public string? Description { get; init; }
    public PriorityEnum Priority { get; init; } = PriorityEnum.Medium;
    public string? Country { get; init; }
    public string? State { get; init; }
    public string? City { get; init; }
    public string? Province { get; init; }
    public string? AddressDetail { get; init; }
    public IFormFileCollection? Files { get; init; }
    public decimal? Latitude { get; init; }
    public decimal? Longitude { get; init; }
}
