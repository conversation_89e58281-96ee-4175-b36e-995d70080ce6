import { notification, Space, Tag } from "antd";
import { useEffect, useState, useRef } from "react";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
} from "@microsoft/signalr";
import MakeCallButton from "./MakeCallButton";
import DoneCallButton from "./DoneCallButton";
import { useTranslation } from "react-i18next";
import { SwapRightOutlined } from "@ant-design/icons";
import ExpandableText from "@/apps/Common/TruncatedDesc";

const CalendarHub = () => {
  const { t } = useTranslation();
  const [api, contextHolder] = notification.useNotification();
  const [connection, setConnection] = useState<any | null>(null);
  const notificationKey = useRef("autoCallNotification");

  useEffect(() => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/calendarhub`, {
        accessTokenFactory: () => {
          return localStorage.getItem("access_token") || "";
        },
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    newConnection
      .start()
      .then(() => {
        console.log("SignalR bağlantısı kuruldu");
        setConnection(newConnection);
      })
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (connection) {
      connection.on("AutoCallStarted", async (data: any) => {
      
        openNotification(data);
      });
    }

    return () => {
      if (connection) {
        connection.off("AutoCallStarted");
      }
    };
  }, [connection]);

  const handleOnClose = () => {
    api.destroy(notificationKey.current);
  };

  const openNotification = (record: any) => {
    if (
      document.querySelector(
        `.ant-notification-notice[data-key="${notificationKey.current}"]`
      )
    ) {
      return;
    }

    const btn = (
      <Space>
        <MakeCallButton phone={record?.phoneNumber} onClose={handleOnClose} />
        <DoneCallButton selectedRecord={record} onClose={handleOnClose} />
      </Space>
    );

    api.open({
      message: (
        <span className="!text-black">{t("calendar.autoDialerTitle")}</span>
      ),
      description: (
        <div className="!flex flex-col gap-1">
          <div className="!flex gap-1 items-center">
            {
              record?.detail &&
              <>
            <ExpandableText
              title=""
              limit={30}
              text={record?.detail ??""}
              textClassName="!text-xs font-bold"
            />
            <SwapRightOutlined />
              </>
            }
            <Tag color="blue">{record?.phoneNumber}</Tag>
          </div>
          <span className="!text-xs !text-black">
            {t("calendar.autoDialerDesc")}
          </span>
        </div>
      ),
      btn,
      closeIcon: null,
      key: notificationKey.current,
      duration: 0,
    });
  };

 

  return <>{contextHolder}</>;
};

export default CalendarHub;
