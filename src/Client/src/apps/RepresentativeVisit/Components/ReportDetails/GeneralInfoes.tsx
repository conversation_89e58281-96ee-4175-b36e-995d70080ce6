import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Typography } from "antd";

const GeneralInfoes = () => {
    const{Text} = Typography
    return ( <>
    <Row gutter={[20,10]} >
        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >Personel:</span>
          <span className="!text-gray-600" ><PERSON></span>
        </Col>
      
        
        <Col xs={24} >
        <Divider className="!m-0" />
        </Col>
        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >Adres:</span>
          <span className="!text-gray-600" >BAHÇELİEVLER MAH.</span>
        </Col>
        <Col xs={24} >
        <Divider className="!m-0" />
        </Col>

        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" ><PERSON><PERSON><PERSON>:</span>
          <span className="!text-gray-600" >25.08.2025 15:46</span>
        </Col>
        
        <Col xs={24} >
        <Divider className="!m-0" />
        </Col>
        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >Ziyaret Sebebi:</span>
          <span className="!text-gray-600" >Yeni Bağlantı (İnsan Kaynakları)</span>
        </Col>
        <Col xs={24} >
        <Divider className="!m-0" />
        </Col>
        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >Görüşülen Yetkili:</span>
          <span className="!text-gray-600" >Yetkili 1</span>
        </Col>
        
        
        <Col xs={24} >
        <Divider className="!m-0" />
        </Col>
        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >Tekrar Ziyaret:</span>
          <span className="!text-gray-600" ></span>
        </Col>
        <Col xs={24} >
        <Divider className="!m-0" />
        </Col>
          <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >	Sonuç:</span>
          <Tag color="blue" >	Bekleniyor</Tag>
        </Col>
         <Col xs={24} >
        <Divider className="!m-0" />
        </Col>
        <Col xs={24} className="flex gap-2 items-center"   >
          <span className="!font-bold" >	Açıklama:</span>
          <span className="!text-gray-600" >	Görüşme olumlu geçti.</span>
        </Col>
        
    </Row>
    </> );
}
 
export default GeneralInfoes;