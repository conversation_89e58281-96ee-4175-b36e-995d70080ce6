import { FC, useState } from "react";
import { Table, Button, Modal, Input, Drawer, Tag } from "antd";
import dayjs from "dayjs";
import Histories from "./Histories";
import { EyeOutlined } from "@ant-design/icons";
import ReportDetailsIndex from "./ReportDetails/ReportDetailsIndex";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import { useTranslation } from "react-i18next";
import ArrayCell from "@/apps/Admin/Pages/Calendar/Components/Table/ArrayCell";

const ListItems: FC<{ mode: "list" | "history" }> = ({ mode }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [comment, setComment] = useState("");
  const [isShowHistoriesDrawer, setIsShowHistoriesDrawer] = useState(false);
  const [isShowReportDetails, setIsShowReportDetails] = useState(false);
  const { t } = useTranslation();
 const columns = [
    {
      title: "Temsilci",
      dataIndex: "personnel",
      key: "personnel",
      width: "10%",
      render: (value: string) => (
        <div
          className="!flex items-center gap-1 cursor-pointer "
          onClick={() => {
            if (mode !== "list") {
              setIsShowReportDetails(true);
            }
          }}
        >
          {mode !== "list" && <EyeOutlined className="!text-blue-500" />}
          <span
            className={` !text-xs ${
              mode === "list" ? "!text-black" : "!text-blue-500"
            }`}
          >
            {value?.replace("Akaydo","")}
          </span>
        </div>
      ),
    },
    ...(mode === "list"
      ? [
          {
            title: "Müşteri",
            dataIndex: "company",
            key: "company",
            render: (text: any) => (
              
              <div
                className="cursor-pointer !text-xs"
                onClick={() => {
                  setIsShowHistoriesDrawer(true);
                }}
              >
                {text.charAt(0).toLocaleUpperCase("tr") +
                  text.slice(1).toLocaleLowerCase("tr")}
              </div>
            ),
          },
        ]
      : []),
   
    {
      title: t("representative.address"),
      dataIndex: "address",
      key: "address",
      render: (text: string) => (
        <ExpandableText
          title={ t("representative.address")}
          limit={10}
          text={text || ""}
          textClassName={"!text-black !text-xs"}
        />
      ),
    },
     {
      title: "Ürünler",
      dataIndex: "products",
      key: "produts",
     
     
      render: (value: any[]) => {
        return (
          <>
            <ArrayCell data={[{Name:"Ürün1"},{Name:"Ürün2"},{Name:"Ürün3"},{Name:"Ürün4"}]} keyField={"Name"} modalTitle="Ürünler" />
              
          </>
        );
      },
    },
    
    {
      title: t("representative.visitDate"),
      dataIndex: "visitDate",
      key: "visitDate",
      render: (value: string) => (
        <span className="!text-xs">{value}</span>
      ),
    },
    {
      title: t("representative.visitReason"),
      dataIndex: "visitReason",
      key: "visitReason",
      render: (text: string) => (
        <ExpandableText
          title={t("representative.visitReason")}
          limit={25}
          text={text || ""}
          textClassName={"!text-black !text-xs"}
        />
      ),
    },
    {
      title: t("representative.contactPerson"),
      dataIndex: "contactPerson",
      key: "contactPerson",
      render: (value: string) => (
        <span className="!text-xs">{value}</span>
      ),
    },
 
    {
      title: t("representative.revisit"),
      dataIndex: "revisit",
      key: "revisit",
      render: (value: string) => (
        <span className="!text-xs">{value}</span>
      ),
    },
    ...(mode === "list"
      ? [
          {
            title: t("representative.description"),
            dataIndex: "description",
            key: "description",
            render: (text: string) => (
              <ExpandableText
                title={t("representative.description")}
                limit={40}
                text={text || ""}
                textClassName={"!text-black !text-xs"}
              />
            ),
          },
        ]
      : []),
    {
  title: t("representative.result"),
  dataIndex: "requestStatus",
  key: "requestStatus",
  render: (status: string) => {
    let color = "blue";
    if (status === "Reddedildi") color = "red";
    if (status === "Kabul Edildi") color = "green";

    if(status)
    {

      return <Tag color={color}>{status}</Tag>;
    }
    else{
      return <Tag color={"orange"}>{"Görüşülemedi"}</Tag>;
    }

  },
},
  ];

  const companyNames = [
    "Ahmet Yılmaz",
  "Mehmet Demir",
  "Ayşe Kaya",
  "Fatma Çelik",
  "Ali Şahin",
  "Zeynep Koç",
  "Hasan Acar",
  "Emine Güneş",
  "Murat Öz",
  "Hüseyin Yıldız",
  "Selin Korkmaz",
  "Burak Uçar",
  "Elif Arslan",
  "Cem Yalçın",
  "Gamze Öztürk",
  "Serkan Eren",
  "Derya Aksoy",
  "Orhan Tekin",
  "Hakan Kaplan",
  ];

  const addresses = [
    "BAHÇELİEVLER MAH.",
    "GAYRETMEN MAH.",
    "BİRLİK ORG. SAN.",
    "DARICA",
    "PLASTİKÇİLER",
    "PELİTLİ MAH.",
    "HİTACHİ",
    "MUALLİMKÖY",
    "MOLAFENERİ",
    "OSMANİYE MAH.",
    "GAZİLER MAH.",
    "SIRAOSGÜLET",
  ];

  const siteNames = [
    "GEBZE",
    "ÇAYIROVA",
    "DARICA",
    "GENEVON",
    "PELİTLİ MAH.",
    "HİTACHİ",
    "MUALLİMKÖY",
    "AKVİRAN",
    "MOLAFENERİ",
    "GAZİLER MAH.",
    "NENENHATUN",
    "SIRAOSGÜLET",
  ];

  const reasons = [
    { label: "Müşteri Ziyareti", value: 1 },
    { label: "Yeni Tanışma", value: 2 },
    { label: "Yeni Bağlantı", value: 3 },
    { label: "Yeni Bağlantı", value: 4 },
  ];

  const units = [
    "Muhasebe",
    "Satış",
    "İnsan Kaynakları",
    "Teknik",
    "Operasyon",
  ];

  const companyTypes = [
    { type: "Müşteri", color: "green" },
    { type: "Potansiyel Müşteri", color: "red" },
    { type: "Yeni Temas", color: "blue" },
  ];

  const descriptions = [
    "Proje hakkında detaylı bilgi verildi.",
    "Yeni bağlantı sağlandı.",
    "Görüşme olumlu geçti.",
    "Teklif sunuldu.",
    "Takip yapılacak.",
  ];

const statuses = ["Bekleniyor", "Reddedildi", "Kabul Edildi"];

const baseDataSource = Array.from({ length: companyNames.length }).map(
  (_, index) => {
    const randomType =
      companyTypes[Math.floor(Math.random() * companyTypes.length)];
    const visitDate = dayjs()
      .subtract(index, "day")
      .hour(Math.floor(Math.random() * 24))
      .minute(Math.floor(Math.random() * 60));

    const revisitYes = Math.random() > 0.5;
    const revisitDate = revisitYes
      ? visitDate.add(Math.floor(Math.random() * 10) + 1, "day")
      : null;

    const reason = reasons[Math.floor(Math.random() * reasons.length)];
    const unit = units[Math.floor(Math.random() * units.length)];

    return {
      key: index,
      personnel: `Personel ${index + 1}`,
      company: companyNames[index % companyNames.length],
      companyType: randomType.type,
      companyTypeColor: randomType.color,
      site: siteNames[index % siteNames.length],
      address: addresses[index % addresses.length],
      visitDate: visitDate.format("DD.MM.YYYY HH:mm"),
      visitReason: `${reason.label} (${unit})`,
      contactPerson: `Yetkili ${index + 1}`,
      opportunity: index % 3 === 0 ? "Bağlantı" : "Yeniden Aktif Çalışma",
      revisit: revisitYes ? revisitDate?.format("DD.MM.YYYY HH:mm") : "",
      description:
        descriptions[Math.floor(Math.random() * descriptions.length)],
      requestStatus: statuses[Math.floor(Math.random() * statuses.length)], // ✅ random status
    };
  }
);


  // 🔹 ekstra 4 tane Akaydo kaydı ekle
  const extraAkaydo = Array.from({ length: 4 }).map((_, index) => {
    const visitDate = dayjs()
      .subtract(index, "day")
      .hour(Math.floor(Math.random() * 24))
      .minute(Math.floor(Math.random() * 60));

    return {
      key: `akaydo-${index}`,
      personnel: `Akaydo Personel ${index + 1}`,
      company: "AKAYDO İNŞAAT ANONİM ŞİRKETİ",
      companyType: "Müşteri",
      companyTypeColor: "green",
      site: "GEBZE",
      address: "BAHÇELİEVLER MAH.",
      visitDate: visitDate.format("DD.MM.YYYY HH:mm"),
      visitReason: "Müşteri Ziyareti (Satış)",
      contactPerson: `Akaydo Yetkili ${index + 1}`,
      opportunity: "Bağlantı",
      revisit: "",
      description: "Özel Akaydo datası",
    };
  });

  const dataSource = [...baseDataSource, ...extraAkaydo];

  // 🔹 mode "history" ise sadece Akaydo olanlar gösterilsin
  const filteredDataSource =
    mode === "history"
      ? dataSource.filter(
          (item) => item.company === "AKAYDO İNŞAAT ANONİM ŞİRKETİ"
        )
      : dataSource;

  return (
    <>
      <Table
        columns={columns}
        dataSource={filteredDataSource}
        pagination={false}
      />

      <Modal
        title={`Yorum - ${selectedRow?.company || ""}`}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={() => {
          console.log("Yorum:", comment);
          setIsModalOpen(false);
          setComment("");
        }}
      >
        <Input.TextArea
          rows={4}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Yorumunuzu yazın..."
        />
      </Modal>

      <Drawer
        title="Ahmet Yılmaz Gçmişi"
        footer={false}
        open={isShowHistoriesDrawer}
        onClose={() => {
          setIsShowHistoriesDrawer(false);
        }}
        width={"95%"}
        bodyStyle={{ background: "#f5f5f5" }}
      >
        <Histories />
      </Drawer>
      <Drawer
        title="Detaylar"
        footer={false}
        open={isShowReportDetails}
        onClose={() => {
          setIsShowReportDetails(false);
        }}
        width={"95%"}
        bodyStyle={{ background: "#f5f5f5" }}
      >
        <ReportDetailsIndex />
      </Drawer>
    </>
  );
};

export default ListItems;
