import React, { useState, useEffect } from "react";
import {
  GoogleMap,
  InfoWindow,
  useJsApiLoader,
} from "@react-google-maps/api";
import { Tag } from "antd";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetDashboardTicketLocations } from "../ServerSideStates";

const containerStyle = { width: "100%", height: "400px" };
const GOOGLE_MAP_LIBRARIES:any[] = ["places"];
// Sorunlar ve adresler dizisi
const sorunlar = [
  "Su Patlağı","<PERSON>gar Kapağı","Çöp Toplama","Aydınlatma Arızası",
  "Yol Bozukluğu","Kanalizasyon Taşması","Park ve Bahçe Bakımı","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Hasarı",
  "<PERSON><PERSON><PERSON><PERSON> Lambası Arızası","<PERSON><PERSON><PERSON><PERSON> Atığı","Çamur<PERSON> Yol","Sokak Hayvanları",
  "Pazar Yeri Temizliği","Gürültü Kirliliği","Ağaç Budama İhtiyacı","Sel Baskını Uyarısı",
  "Yağmur Suyu Kanalı Tıkanıklığı","Toprak Kayması","Güvenlik Sorunu","Bina Hasarı"
];

const kayseriAdresleri = [
  "İstasyon Caddesi","Atatürk Bulvarı","Hacılar Caddesi","Talas Caddesi",
  "Melikgazi Caddesi","Kocasinan Caddesi","Hunat Caddesi","Saray Caddesi",
  "Küçük Sanayi Caddesi","Büyükşehir Caddesi"
];

// Rastgele marker oluşturma fonksiyonu
const generateRandomMarkers = (count: number) => {
  const markers = [];
  const minLat = 38.6, maxLat = 38.8, minLng = 35.4, maxLng = 35.6;

  for (let i = 0; i < count; i++) {
    const lat = Math.random() * (maxLat - minLat) + minLat;
    const lng = Math.random() * (maxLng - minLng) + minLng;
    const label = sorunlar[Math.floor(Math.random() * sorunlar.length)];
    const address = kayseriAdresleri[Math.floor(Math.random() * kayseriAdresleri.length)];
    markers.push({ lat, lng, id: i, label, address });
  }
  return markers;
};

const TicketsMap: React.FC = () => {
  const { isLoaded, loadError } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAP_API || "",
    libraries: GOOGLE_MAP_LIBRARIES,
    language: "tr",
  });
    const { filter } = useSelector((state: RootState) => state.dashboard);
    const tickets = useGetDashboardTicketLocations(filter);

  const [markers, setMarkers] = useState<any[]>([]);
  const [activeMarker, setActiveMarker] = useState<number | null>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);

  useEffect(() => {
    if (isLoaded) {
      const generatedMarkers = generateRandomMarkers(20);
      setMarkers(generatedMarkers);
      
    }
  }, [isLoaded]);

  useEffect(() => {
    if (map && markers.length > 0) {
      const markerObjects = markers.map((m) => {
        const marker = new google.maps.Marker({
          position: { lat: m.lat, lng: m.lng },
          map,
          title: m.label,
        });

        marker.addListener("click", () => {
          setActiveMarker(m.id);
        });

        return marker;
      });

      new MarkerClusterer({ markers: markerObjects, map });
    }
  }, [map, markers]);

  if (loadError) return <p>Harita yüklenemedi</p>;
  if (!isLoaded) return <p>Harita yükleniyor...</p>;

 

  return (
    <GoogleMap
      mapContainerStyle={containerStyle}
      center={{ lat: 38.7225, lng: 35.4875 }}
      zoom={10}
      onLoad={setMap}
    >
      {markers.map(
        (marker) =>
          activeMarker&&marker.id&&activeMarker === marker.id && (
            <InfoWindow
              key={marker.id}
              position={{ lat: marker.lat, lng: marker.lng }}
              onCloseClick={() => setActiveMarker(null)}
            >
              <div className="!flex flex-col gap-2" >
        

                <span className="!text-sm font-bold ">{marker.label}</span>
     
                <span className="!text-gray-500 !text-xs" >{marker.address}</span>
                <div>
                  <Tag color="green">Durum: {Math.floor(Math.random() * 100)}</Tag>
                  <Tag color="blue">Sayı: {Math.floor(Math.random() * 1000)}</Tag>
                </div>
              </div>
            </InfoWindow>
          )
      )}
    </GoogleMap>
  );
};

export default TicketsMap;
