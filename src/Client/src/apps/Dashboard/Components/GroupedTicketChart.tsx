import { RootState } from "@/store/Reducers";
import { Tooltip as Antd<PERSON>ooltip, Skeleton } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useGetDashboardGroupTicket } from "../ServerSideStates";

// Tooltip özelleştirme
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div style={{ background: "#fff", padding: 10, border: "1px solid #ccc" }}>
        <p>{label || "Genel"}</p>
        {payload
          .filter((p: any) => p.value > 0)
          .map((p: any) => (
            <p key={p.name} style={{ color: p.fill }}>
              {(p.name && p.name.trim() !== "" ? p.name : "")}:{" "}
              {p.value}
            </p>
          ))}
      </div>
    );
  }
  return null;
};

const GroupedTicketTitles: React.FC = () => {
  const { filter } = useSelector((state: RootState) => state.dashboard);
  const tickets = useGetDashboardGroupTicket(filter);
  const { t } = useTranslation();

  const data =
    tickets.data?.Value?.map((item: any) => ({
      label: item.Label,
      sayi: item.Count,
      status:
        item.Status?.map((s: any) => ({
          label: s.Label, // boş olanlar da tutulacak
          sayi: s.Count,
        })) || [],
    })) || [];

  
  const allStatusLabels = Array.from(
    new Set(data.flatMap((item) => item.status.map((s) => s.label)))
  );

  // Status renkleri
  const colors = [
    "#ff7f0e", // turuncu
    "#2ca02c", // yeşil
    "#d62728", // kırmızı
    "#9467bd", // mor
    "#8c564b",
    "#e377c2",
  ];

  
  const bars = allStatusLabels.map((statusLabel, index) => (
    <Bar
      key={statusLabel || ""}
      dataKey={(entry: any) => {
        const s = entry.status.find((st: any) => st.label === statusLabel);
        return s ? s.sayi : 0;
      }}
      name={statusLabel && statusLabel.trim() !== "" ? statusLabel : " "}
      stackId="a"
      fill={colors[index % colors.length]}
      barSize={20}
    />
  ));


  bars.push(
    <Bar
      key="Sayı"
      dataKey="sayi"
      name="Sayı"
      stackId="a"
      fill="#1f77b4"
      barSize={20}
    />
  );

  const CustomXAxisTick = ({ x, y, payload }: any) => {
    const name = payload.value;
    const shortName = name.length > 12 ? `${name.substring(0, 12)}..` : name;
    return (
      <g transform={`translate(${x},${y})`}>
        <AntdTooltip title={name}>
          <text
            x={0}
            y={0}
            dy={4}
            textAnchor="end"
            fill="#666"
            style={{ cursor: "pointer", fontSize: "12px" }}
          >
            {shortName}
          </text>
        </AntdTooltip>
      </g>
    );
  };

  return (
    <div style={{ width: 700, height: 400 }} className="!py-4">
      <Skeleton loading={tickets.isLoading ||tickets.isFetching} >

      <div>
        <span className="!font-bold !text-sm">
          {t("dashboard.ticketTitlesTopic")}
        </span>
      </div>
      <ResponsiveContainer>
        <BarChart layout="horizontal" data={data} className={"!my-4"}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="label" type="category" tick={<CustomXAxisTick />} />
          <YAxis type="number" />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          {bars}
        </BarChart>
      </ResponsiveContainer>
      </Skeleton>
    </div>
  );
};

export default GroupedTicketTitles;
