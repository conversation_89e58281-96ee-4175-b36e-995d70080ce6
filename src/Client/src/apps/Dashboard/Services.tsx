import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { get } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getAllDashboardInfoes = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = filter ? CreateUrlFilter(filter) : null;

  const url = `${endpoints.getAllDashboardInfoes}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getDashboardGroupTicketTitle = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = filter ? CreateUrlFilter(filter) : null;

  const url = `${endpoints.getDashboardGroupTicketTitle}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getDashboardTicketLocations = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = filter ? CreateUrlFilter(filter) : null;

  const url = `${endpoints.getDashboardTicketLocations}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
