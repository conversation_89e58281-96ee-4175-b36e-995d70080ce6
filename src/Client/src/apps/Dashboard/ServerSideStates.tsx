import { useQuery } from "react-query";
import endpoints from "./EndPoints";
import { getAllDashboardInfoes, getDashboardGroupTicketTitle, getDashboardTicketLocations } from "./Services";

export const useGetDashboardSummaryInfoes = (filter?: any) => {
  const query = useQuery(
    [endpoints.getAllDashboardInfoes, filter],
    () => {
      return getAllDashboardInfoes(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetDashboardGroupTicket= (filter?: any) => {
  const query = useQuery(
    [endpoints.getDashboardGroupTicketTitle, filter],
    () => {
      return getDashboardGroupTicketTitle(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetDashboardTicketLocations= (filter?: any) => {
  const query = useQuery(
    [endpoints.getDashboardTicketLocations, filter],
    () => {
      return getDashboardTicketLocations(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};